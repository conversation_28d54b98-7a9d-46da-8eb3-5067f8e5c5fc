package affiliate

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	natsModel "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/nats"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo"
)

// AffiliateService handles business logic for affiliate transactions
type AffiliateService struct {
	affiliateRepo repo.AffiliateRepositoryInterface
	userRepo      repo.UserRepositoryInterface
}

// NewAffiliateService creates a new affiliate service
func NewAffiliateService() AffiliateServiceInterface {
	return &AffiliateService{
		affiliateRepo: repo.NewAffiliateRepository(),
		userRepo:      repo.NewUserRepository(),
	}
}

// ProcessAffiliateTransaction processes an affiliate transaction event from NATS
func (s *AffiliateService) ProcessAffiliateTransaction(ctx context.Context, txEvent *natsModel.AffiliateTxEvent) error {
	global.GVA_LOG.Info("Processing affiliate transaction",
		zap.String("order_id", txEvent.ID.String()),
		zap.String("user_id", txEvent.UserId),
		zap.String("transaction_type", string(txEvent.TransactionType)),
		zap.String("status", string(txEvent.Status)))

	// Check if transaction already exists
	existingTx, err := s.affiliateRepo.GetAffiliateTransactionByOrderID(ctx, txEvent.ID)
	if err != nil && err != gorm.ErrRecordNotFound {
		return fmt.Errorf("failed to check existing transaction: %w", err)
	}

	// If transaction exists, update it
	if existingTx != nil {
		return s.updateExistingTransaction(ctx, existingTx, txEvent)
	}

	// Create new transaction
	return s.createNewTransaction(ctx, txEvent)
}

// ProcessSolPriceUpdate processes a SOL price update event from NATS
func (s *AffiliateService) ProcessSolPriceUpdate(ctx context.Context, priceEvent *natsModel.SolPriceEvent) error {
	global.GVA_LOG.Debug("Processing SOL price update",
		zap.String("symbol", priceEvent.GetSymbol()),
		zap.String("price", priceEvent.UsdPrice.String()),
		zap.Time("timestamp", priceEvent.GetTime()))

	// Create price snapshot
	snapshot := &model.SolPriceSnapshot{
		Price:     priceEvent.UsdPrice,
		Symbol:    priceEvent.GetSymbol(),
		ChainID:   fmt.Sprintf("%d", priceEvent.ChainId), // Convert int to string
		Address:   priceEvent.Token,                      // Use token address
		Timestamp: priceEvent.GetTime(),                  // Convert Unix timestamp to time.Time
	}

	if err := s.affiliateRepo.CreateSolPriceSnapshot(ctx, snapshot); err != nil {
		return fmt.Errorf("failed to create SOL price snapshot: %w", err)
	}

	global.GVA_LOG.Debug("SOL price snapshot created successfully",
		zap.String("symbol", priceEvent.GetSymbol()),
		zap.Uint("snapshot_id", snapshot.ID))

	return nil
}

// createNewTransaction creates a new affiliate transaction
func (s *AffiliateService) createNewTransaction(ctx context.Context, txEvent *natsModel.AffiliateTxEvent) error {
	// Parse user ID
	userID, err := uuid.Parse(txEvent.UserId)
	if err != nil {
		return fmt.Errorf("invalid user ID: %w", err)
	}

	// Get user to check if they have a referrer
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		global.GVA_LOG.Warn("User not found, creating transaction without referral info",
			zap.String("user_id", txEvent.UserId),
			zap.Error(err))
	}

	// Get referral information
	var referrerID *uuid.UUID
	var referralDepth int
	var commissionRate decimal.Decimal
	var commissionAmount decimal.Decimal

	if user != nil {
		referralInfo, err := s.userRepo.GetReferralInfo(ctx, userID)
		if err == nil && referralInfo != nil && referralInfo.ReferrerID != nil {
			referrerID = referralInfo.ReferrerID
			referralDepth = referralInfo.Depth

			// Calculate commission (example: 0.1% for direct referrals)
			commissionRate = decimal.NewFromFloat(0.001) // 0.1%
			commissionAmount = txEvent.QuoteAmount.Mul(commissionRate)
		}
	}

	// Convert NATS model types to database model types
	transactionType := model.TransactionType(txEvent.TransactionType)
	orderType := model.OrderType(txEvent.Type)
	status := model.TransactionStatus(txEvent.Status)

	// Calculate volume in USD (assuming quote amount is in USD for now)
	volumeUSD := txEvent.QuoteAmount

	// Create affiliate transaction
	affiliateTx := &model.AffiliateTransaction{
		OrderID:          txEvent.ID,
		CreatedAt:        txEvent.CreatedAt,
		TransactionType:  transactionType,
		Type:             orderType,
		ChainID:          txEvent.ChainId,
		BaseAddress:      txEvent.BaseAddress,
		BaseSymbol:       txEvent.BaseSymbol,
		QuoteAddress:     txEvent.QuoteAddress,
		QuoteSymbol:      txEvent.QuoteSymbol,
		UserID:           userID,
		UserAddress:      txEvent.UserAddress,
		BaseAmount:       txEvent.BaseAmount,
		QuoteAmount:      txEvent.QuoteAmount,
		TotalFee:         txEvent.TotalFee,
		Slippage:         txEvent.Slippage,
		Status:           status,
		TxHash:           txEvent.Txid,
		MevProtect:       txEvent.MevProtect,
		ReferrerID:       referrerID,
		ReferralDepth:    referralDepth,
		CommissionRate:   commissionRate,
		CommissionAmount: commissionAmount,
		CommissionPaid:   false,
		VolumeUSD:        volumeUSD,
	}

	if err := s.affiliateRepo.CreateAffiliateTransaction(ctx, affiliateTx); err != nil {
		return fmt.Errorf("failed to create affiliate transaction: %w", err)
	}

	global.GVA_LOG.Info("Affiliate transaction created successfully",
		zap.String("order_id", txEvent.ID.String()),
		zap.Uint("transaction_id", affiliateTx.ID),
		zap.String("commission_amount", commissionAmount.String()))

	return nil
}

// updateExistingTransaction updates an existing affiliate transaction
func (s *AffiliateService) updateExistingTransaction(ctx context.Context, existingTx *model.AffiliateTransaction, txEvent *natsModel.AffiliateTxEvent) error {
	// Update fields that might change
	existingTx.Status = model.TransactionStatus(txEvent.Status)
	existingTx.TxHash = txEvent.Txid
	existingTx.BaseAmount = txEvent.BaseAmount
	existingTx.QuoteAmount = txEvent.QuoteAmount
	existingTx.TotalFee = txEvent.TotalFee
	existingTx.VolumeUSD = txEvent.QuoteAmount // Update volume

	// Recalculate commission if transaction is completed and commission hasn't been paid
	if existingTx.Status == model.StatusCompleted && !existingTx.CommissionPaid && existingTx.ReferrerID != nil {
		existingTx.CommissionAmount = existingTx.VolumeUSD.Mul(existingTx.CommissionRate)
	}

	if err := s.affiliateRepo.UpdateAffiliateTransaction(ctx, existingTx); err != nil {
		return fmt.Errorf("failed to update affiliate transaction: %w", err)
	}

	global.GVA_LOG.Info("Affiliate transaction updated successfully",
		zap.String("order_id", txEvent.ID.String()),
		zap.Uint("transaction_id", existingTx.ID),
		zap.String("status", string(existingTx.Status)))

	return nil
}

// GetUserTransactions retrieves affiliate transactions for a user
func (s *AffiliateService) GetUserTransactions(ctx context.Context, userID uuid.UUID, limit, offset int) ([]model.AffiliateTransaction, error) {
	return s.affiliateRepo.GetAffiliateTransactionsByUserID(ctx, userID, limit, offset)
}

// GetReferrerTransactions retrieves affiliate transactions for a referrer
func (s *AffiliateService) GetReferrerTransactions(ctx context.Context, referrerID uuid.UUID, limit, offset int) ([]model.AffiliateTransaction, error) {
	return s.affiliateRepo.GetAffiliateTransactionsByReferrerID(ctx, referrerID, limit, offset)
}

// GetUnpaidCommissions retrieves unpaid commissions for a referrer
func (s *AffiliateService) GetUnpaidCommissions(ctx context.Context, referrerID uuid.UUID) ([]model.AffiliateTransaction, error) {
	return s.affiliateRepo.GetUnpaidCommissions(ctx, referrerID)
}

// MarkCommissionsAsPaid marks commissions as paid
func (s *AffiliateService) MarkCommissionsAsPaid(ctx context.Context, transactionIDs []uint) error {
	return s.affiliateRepo.MarkCommissionAsPaid(ctx, transactionIDs)
}

// GetUserStats retrieves statistics for a user
func (s *AffiliateService) GetUserStats(ctx context.Context, userID uuid.UUID) (*UserStats, error) {
	totalVolume, err := s.affiliateRepo.GetUserTotalVolume(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user total volume: %w", err)
	}

	return &UserStats{
		UserID:      userID,
		TotalVolume: totalVolume,
	}, nil
}

// GetReferrerStats retrieves statistics for a referrer
func (s *AffiliateService) GetReferrerStats(ctx context.Context, referrerID uuid.UUID) (*ReferrerStats, error) {
	totalCommission, err := s.affiliateRepo.GetReferrerTotalCommission(ctx, referrerID)
	if err != nil {
		return nil, fmt.Errorf("failed to get referrer total commission: %w", err)
	}

	unpaidCommission, err := s.affiliateRepo.GetReferrerUnpaidCommission(ctx, referrerID)
	if err != nil {
		return nil, fmt.Errorf("failed to get referrer unpaid commission: %w", err)
	}

	return &ReferrerStats{
		ReferrerID:       referrerID,
		TotalCommission:  totalCommission,
		UnpaidCommission: unpaidCommission,
		PaidCommission:   totalCommission.Sub(unpaidCommission),
	}, nil
}

// UserStats represents user statistics
type UserStats struct {
	UserID      uuid.UUID       `json:"user_id"`
	TotalVolume decimal.Decimal `json:"total_volume"`
}

// ReferrerStats represents referrer statistics
type ReferrerStats struct {
	ReferrerID       uuid.UUID       `json:"referrer_id"`
	TotalCommission  decimal.Decimal `json:"total_commission"`
	UnpaidCommission decimal.Decimal `json:"unpaid_commission"`
	PaidCommission   decimal.Decimal `json:"paid_commission"`
}
