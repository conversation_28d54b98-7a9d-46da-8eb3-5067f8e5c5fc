package main

import (
	"context"
	"fmt"
	"os"
	"os/signal"
	"syscall"
	"time"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/app"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	natsClient "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/nats"
)

func main() {
	var s = app.GraphqlServer{}
	s.Initialize()

	go func() {
		s.Run()
	}()

	quit := make(chan os.Signal, 1)

	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	fmt.Println("Shutting down server...")

	// The context is used to inform the server it has 30 seconds to finish
	// the request it is currently handling
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := s.HttpServer.Shutdown(ctx); err != nil {
		fmt.Printf("Server forced to shutdown: %s\n", err)
	}

	// Cancel NATS subscribers context
	if s.Cancel != nil {
		s.Cancel()
	}

	// Close NATS connection
	if global.GVA_NATS != nil {
		if natsClient, ok := global.GVA_NATS.(*natsClient.NATSClient); ok {
			natsClient.Close()
		}
	}

	if global.GVA_DB != nil {
		db, _ := global.GVA_DB.DB()
		defer db.Close()
	}

	fmt.Println("Server exiting")
}
