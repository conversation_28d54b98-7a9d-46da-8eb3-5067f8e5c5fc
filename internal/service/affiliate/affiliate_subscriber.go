package affiliate

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/nats-io/nats.go"
	"go.uber.org/zap"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	natsClient "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/nats"
	natsModel "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/nats"
)

// AffiliateSubscriberService handles NATS subscription for affiliate transactions and SOL price updates
type AffiliateSubscriberService struct {
	natsClient       natsClient.Subscriber
	affiliateService AffiliateServiceInterface
}

// AffiliateServiceInterface defines the interface for affiliate service operations
type AffiliateServiceInterface interface {
	ProcessAffiliateTransaction(ctx context.Context, txEvent *natsModel.AffiliateTxEvent) error
	ProcessSolPriceUpdate(ctx context.Context, priceEvent *natsModel.SolPriceEvent) error
}

// NewAffiliateSubscriberService creates a new affiliate subscriber service
func NewAffiliateSubscriberService(natsClient natsClient.Subscriber, affiliateService AffiliateServiceInterface) *AffiliateSubscriberService {
	return &AffiliateSubscriberService{
		natsClient:       natsClient,
		affiliateService: affiliateService,
	}
}

// Start begins listening to NATS streams for affiliate transactions and SOL price updates
func (s *AffiliateSubscriberService) Start(ctx context.Context) error {
	global.GVA_LOG.Info("Starting AffiliateSubscriberService")

	// Create or ensure the affiliate stream exists
	_, err := s.natsClient.AddStream(&nats.StreamConfig{
		Name:     natsModel.AffiliateStream,
		Subjects: []string{natsModel.AffiliateTxSubject, natsModel.SolPriceSubject},
		Storage:  nats.FileStorage,
	})
	if err != nil {
		return fmt.Errorf("failed to create affiliate stream: %w", err)
	}

	// Start affiliate transaction subscriber
	if err := s.startAffiliateTxSubscriber(ctx); err != nil {
		return fmt.Errorf("failed to start affiliate transaction subscriber: %w", err)
	}

	// Start SOL price subscriber
	if err := s.startSolPriceSubscriber(ctx); err != nil {
		return fmt.Errorf("failed to start SOL price subscriber: %w", err)
	}

	global.GVA_LOG.Info("AffiliateSubscriberService started successfully")
	return nil
}

// startAffiliateTxSubscriber starts the subscriber for affiliate transactions
func (s *AffiliateSubscriberService) startAffiliateTxSubscriber(ctx context.Context) error {
	global.GVA_LOG.Info("Starting affiliate transaction subscriber",
		zap.String("subject", natsModel.AffiliateTxSubject))

	sub, err := s.natsClient.SubscribeJS(natsModel.AffiliateTxSubject, func(msg *nats.Msg) {
		s.handleAffiliateTxMessage(ctx, msg)
	},
		nats.Durable(natsModel.AffiliateTxConsumer),
		nats.ManualAck(),
		nats.BindStream(natsModel.AffiliateStream),
		nats.AckWait(30*time.Second),
	)

	if err != nil {
		return fmt.Errorf("failed to subscribe to affiliate transaction subject: %w", err)
	}

	// Handle graceful shutdown
	go func() {
		<-ctx.Done()
		global.GVA_LOG.Info("Unsubscribing from affiliate transaction subject")
		if err := sub.Unsubscribe(); err != nil {
			global.GVA_LOG.Error("Failed to unsubscribe from affiliate transaction subject", zap.Error(err))
		}
	}()

	return nil
}

// startSolPriceSubscriber starts the subscriber for SOL price updates
func (s *AffiliateSubscriberService) startSolPriceSubscriber(ctx context.Context) error {
	global.GVA_LOG.Info("Starting SOL price subscriber",
		zap.String("subject", natsModel.SolPriceSubject))

	sub, err := s.natsClient.SubscribeJS(natsModel.SolPriceSubject, func(msg *nats.Msg) {
		s.handleSolPriceMessage(ctx, msg)
	},
		nats.Durable(natsModel.SolPriceConsumer),
		nats.ManualAck(),
		nats.BindStream(natsModel.AffiliateStream),
		nats.AckWait(30*time.Second),
	)

	if err != nil {
		return fmt.Errorf("failed to subscribe to SOL price subject: %w", err)
	}

	// Handle graceful shutdown
	go func() {
		<-ctx.Done()
		global.GVA_LOG.Info("Unsubscribing from SOL price subject")
		if err := sub.Unsubscribe(); err != nil {
			global.GVA_LOG.Error("Failed to unsubscribe from SOL price subject", zap.Error(err))
		}
	}()

	return nil
}

// handleAffiliateTxMessage processes affiliate transaction messages from NATS
func (s *AffiliateSubscriberService) handleAffiliateTxMessage(ctx context.Context, msg *nats.Msg) {
	var txEvent natsModel.AffiliateTxEvent
	if err := json.Unmarshal(msg.Data, &txEvent); err != nil {
		global.GVA_LOG.Error("Failed to unmarshal affiliate transaction message",
			zap.Error(err),
			zap.String("data", string(msg.Data)))
		msg.Ack()
		return
	}

	global.GVA_LOG.Info("Received affiliate transaction",
		zap.String("order_id", txEvent.ID.String()),
		zap.String("user_id", txEvent.UserId),
		zap.String("transaction_type", string(txEvent.TransactionType)),
		zap.String("status", string(txEvent.Status)))

	// Process the transaction
	if err := s.affiliateService.ProcessAffiliateTransaction(ctx, &txEvent); err != nil {
		global.GVA_LOG.Error("Failed to process affiliate transaction",
			zap.Error(err),
			zap.String("order_id", txEvent.ID.String()))
		// Don't ack the message so it will be redelivered
		return
	}

	// Acknowledge the message
	msg.Ack()
}

// handleSolPriceMessage processes SOL price update messages from NATS
func (s *AffiliateSubscriberService) handleSolPriceMessage(ctx context.Context, msg *nats.Msg) {
	var priceEvent natsModel.SolPriceEvent
	if err := json.Unmarshal(msg.Data, &priceEvent); err != nil {
		global.GVA_LOG.Error("Failed to unmarshal SOL price message",
			zap.Error(err),
			zap.String("data", string(msg.Data)))
		msg.Ack()
		return
	}

	global.GVA_LOG.Debug("Received SOL price update",
		zap.String("symbol", priceEvent.Symbol),
		zap.String("price", priceEvent.Price.String()),
		zap.Time("timestamp", priceEvent.Timestamp))

	// Process the price update
	if err := s.affiliateService.ProcessSolPriceUpdate(ctx, &priceEvent); err != nil {
		global.GVA_LOG.Error("Failed to process SOL price update",
			zap.Error(err),
			zap.String("symbol", priceEvent.Symbol))
		// Don't ack the message so it will be redelivered
		return
	}

	// Acknowledge the message
	msg.Ack()
}
