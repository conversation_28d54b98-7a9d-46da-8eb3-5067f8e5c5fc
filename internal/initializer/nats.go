package initializer

import (
	"context"
	"sync"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	natsClient "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/nats"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/affiliate"
	"go.uber.org/zap"
)

var (
	natsClientInstance     *natsClient.NATSClient
	natsMemeClientInstance *natsClient.NATSClient
	// natsDexClientInstance  *natsClient.NATSClient // Temporarily disabled
	onceInitNats     sync.Once
	onceInitNatsMeme sync.Once
	// onceInitNatsDex        sync.Once // Temporarily disabled
)

// InitNats initializes the NATS client
func InitNats() {
	onceInitNats.Do(func() {
		global.GVA_LOG.Info("Initializing NATS client...")

		// Initialize NATS client
		natsClientInstance = natsClient.InitNatsJetStream(global.GVA_CONFIG.Nats)

		// Set global NATS client
		global.GVA_NATS = natsClientInstance

		global.GVA_LOG.Info("NATS client initialized successfully")
	})
}

// InitNatsMeme initializes the NATS Meme client
func InitNatsMeme() {
	onceInitNatsMeme.Do(func() {
		if global.GVA_CONFIG.NatsMeme.URL == "" {
			global.GVA_LOG.Info("NATS Meme URL not configured, skipping initialization")
			return
		}

		global.GVA_LOG.Info("Initializing NATS Meme client...")

		// Initialize NATS Meme client
		natsMemeClientInstance = natsClient.InitNatsJetStream(global.GVA_CONFIG.NatsMeme)

		// Set global NATS Meme client
		global.GVA_NATS_MEME = natsMemeClientInstance

		global.GVA_LOG.Info("NATS Meme client initialized successfully")
	})
}

// InitNatsDex initializes the NATS Dex client - Temporarily disabled
// func InitNatsDex() {
// 	onceInitNatsDex.Do(func() {
// 		if global.GVA_CONFIG.NatsDex.URL == "" {
// 			global.GVA_LOG.Info("NATS Dex URL not configured, skipping initialization")
// 			return
// 		}
//
// 		global.GVA_LOG.Info("Initializing NATS Dex client...")
//
// 		// Initialize NATS Dex client
// 		natsDexClientInstance = natsClient.InitNatsJetStream(global.GVA_CONFIG.NatsDex)
//
// 		// Set global NATS Dex client
// 		global.GVA_NATS_DEX = natsDexClientInstance
//
// 		global.GVA_LOG.Info("NATS Dex client initialized successfully")
// 	})
// }

// StartAffiliateSubscriber starts the affiliate subscriber service
func StartAffiliateSubscriber(ctx context.Context) error {
	if natsClientInstance == nil {
		global.GVA_LOG.Error("NATS client not initialized")
		return nil
	}

	global.GVA_LOG.Info("Starting affiliate subscriber service...")

	// Create affiliate service
	affiliateService := affiliate.NewAffiliateService()

	// Create affiliate subscriber service
	subscriberService := affiliate.NewAffiliateSubscriberService(natsClientInstance, affiliateService)

	// Start the subscriber service
	if err := subscriberService.Start(ctx); err != nil {
		global.GVA_LOG.Error("Failed to start affiliate subscriber service", zap.Error(err))
		return err
	}

	global.GVA_LOG.Info("Affiliate subscriber service started successfully")
	return nil
}

// CloseNats closes all NATS connections
func CloseNats() {
	if natsClientInstance != nil {
		global.GVA_LOG.Info("Closing NATS connection...")
		natsClientInstance.Close()
		global.GVA_LOG.Info("NATS connection closed")
	}
}

// CloseNatsMeme closes the NATS Meme connection
func CloseNatsMeme() {
	if natsMemeClientInstance != nil {
		global.GVA_LOG.Info("Closing NATS Meme connection...")
		natsMemeClientInstance.Close()
		global.GVA_LOG.Info("NATS Meme connection closed")
	}
}

// CloseNatsDex closes the NATS Dex connection - Temporarily disabled
// func CloseNatsDex() {
// 	if natsDexClientInstance != nil {
// 		global.GVA_LOG.Info("Closing NATS Dex connection...")
// 		natsDexClientInstance.Close()
// 		global.GVA_LOG.Info("NATS Dex connection closed")
// 	}
// }

// CloseAllNats closes all NATS connections
func CloseAllNats() {
	CloseNats()
	CloseNatsMeme()
	// CloseNatsDex() // Temporarily disabled
}
