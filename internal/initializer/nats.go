package initializer

import (
	"context"
	"sync"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	natsClient "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/nats"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/affiliate"
	"go.uber.org/zap"
)

var (
	natsClientInstance *natsClient.NATSClient
	onceInitNats       sync.Once
)

// InitNats initializes the NATS client
func InitNats() {
	onceInitNats.Do(func() {
		global.GVA_LOG.Info("Initializing NATS client...")
		
		// Initialize NATS client
		natsClientInstance = natsClient.InitNatsJetStream(global.GVA_CONFIG.Nats)
		
		// Set global NATS client
		global.GVA_NATS = natsClientInstance
		
		global.GVA_LOG.Info("NATS client initialized successfully")
	})
}

// StartAffiliateSubscriber starts the affiliate subscriber service
func StartAffiliateSubscriber(ctx context.Context) error {
	if natsClientInstance == nil {
		global.GVA_LOG.Error("NATS client not initialized")
		return nil
	}

	global.GVA_LOG.Info("Starting affiliate subscriber service...")

	// Create affiliate service
	affiliateService := affiliate.NewAffiliateService()

	// Create affiliate subscriber service
	subscriberService := affiliate.NewAffiliateSubscriberService(natsClientInstance, affiliateService)

	// Start the subscriber service
	if err := subscriberService.Start(ctx); err != nil {
		global.GVA_LOG.Error("Failed to start affiliate subscriber service", zap.Error(err))
		return err
	}

	global.GVA_LOG.Info("Affiliate subscriber service started successfully")
	return nil
}

// CloseNats closes the NATS connection
func CloseNats() {
	if natsClientInstance != nil {
		global.GVA_LOG.Info("Closing NATS connection...")
		natsClientInstance.Close()
		global.GVA_LOG.Info("NATS connection closed")
	}
}
