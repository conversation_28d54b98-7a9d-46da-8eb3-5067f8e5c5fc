package config

type Server struct {
	JWT      JWT             `mapstructure:"jwt" json:"jwt" yaml:"jwt"`
	Zap      Zap             `mapstructure:"zap" json:"zap" yaml:"zap"`
	Redis    Redis           `mapstructure:"redis" json:"redis" yaml:"redis"`
	Email    Email           `mapstructure:"email" json:"email" yaml:"email"`
	System   System          `mapstructure:"system" json:"system" yaml:"system"`
	Captcha  Captcha         `mapstructure:"captcha" json:"captcha" yaml:"captcha"`
	Pgsql    Pgsql           `mapstructure:"pgsql" json:"pgsql" yaml:"pgsql"`
	Cors     CORS            `mapstructure:"cors" json:"cors" yaml:"cors"`
	DBList   []SpecializedDB `mapstructure:"db-list" json:"db-list" yaml:"db-list"`
	Local    Local           `mapstructure:"local" json:"local" yaml:"local"`
	Autocode Autocode        `mapstructure:"autocode" json:"autocode" yaml:"autocode"`
	Nats     Nats            `mapstructure:"nats" json:"nats" yaml:"nats"`
	NatsMeme Nats            `mapstructure:"nats-meme" json:"nats-meme" yaml:"nats-meme"`
	// NatsDex  Nats            `mapstructure:"nats-dex" json:"nats-dex" yaml:"nats-dex"` // Temporarily disabled - not yet implemented for affiliate events
}
